import pandas as pd

# Step 1: Load the CSV file
csv_path = 'Data/Contratos2.csv'

# The CSV file has a special format where all data is in one column with semicolon-separated values
# Let's read it properly
with open(csv_path, 'r', encoding='utf-8') as file:
    lines = file.readlines()

print(f"Reading {csv_path}...")
print(f"Found {len(lines)} lines in the file")

# Parse the data
data_rows = []
for i, line in enumerate(lines[1:], 1):  # Skip header, start counting from 1
    # Remove quotes and newlines, then split by semicolon
    line = line.strip().strip('"')
    if line:  # Skip empty lines
        parts = line.split(';')
        if len(parts) >= 3:  # Ensure we have at least 3 parts
            data_rows.append(parts)
            print(f"Row {i}: Found {len(parts)} parts")
        else:
            print(f"Row {i}: Skipped (only {len(parts)} parts): {line[:50]}...")

# Create DataFrame with proper column names
# Note: The original data only has 3 columns, but document_generator.py expects 4
# We'll add an empty morada_endereco_eletronico column to match the expected format
columns = ['designacao_da_empresa', 'designacao_do_contrato', 'ref_do_contrato']
df = pd.DataFrame(data_rows, columns=columns)

# Add the missing column that document_generator.py expects
df['morada_endereco_eletronico'] = ''  # Empty string for missing address/email data

print(f"\nSuccessfully loaded {len(df)} rows")
print("Columns:", df.columns.tolist())
print("\nFirst few rows:")
print(df.head())

# Step 2: Save the data in the format expected by document_generator.py
# The document generator expects semicolon-separated CSV with UTF-8 encoding

# Save the file in the format expected by document_generator.py (semicolon-separated)
output_path_for_generator = 'Contratos2_for_generator.csv'
try:
    # Use semicolon separator and UTF-8 encoding as expected by document_generator.py
    df[['designacao_da_empresa', 'designacao_do_contrato', 'ref_do_contrato', 'morada_endereco_eletronico']].to_csv(
        output_path_for_generator, index=False, sep=';', encoding='utf-8'
    )
    print(f"\nDocument generator compatible file saved to {output_path_for_generator} (UTF-8, semicolon-separated)")
except Exception as e:
    print(f"Failed to create document generator file: {e}")

# Also save the combined version for other uses (with combined column)
df['combined'] = df.apply(lambda row: ','.join(row.values.astype(str)), axis=1)

output_path_combined = 'Contratos2_combined.csv'
try:
    # Use comma separator for the combined version
    df.to_csv(output_path_combined, index=False, sep=',', encoding='utf-8-sig')
    print(f"Combined data saved to {output_path_combined} (UTF-8 with BOM, comma-separated)")
except Exception as e:
    print(f"Failed to create combined file: {e}")

print(f"\nSummary:")
print(f"- {output_path_for_generator}: Ready for document_generator.py (semicolon-separated)")
print(f"- {output_path_combined}: Combined data with all columns (comma-separated)")
print(f"- Both files contain {len(df)} rows with proper Portuguese character encoding")