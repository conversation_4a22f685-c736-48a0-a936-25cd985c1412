import pandas as pd

# Step 1: Load the CSV file
csv_path = 'Contratos2.csv'

# The CSV file has a special format where all data is in one column with semicolon-separated values
# Let's read it properly
with open(csv_path, 'r', encoding='utf-8') as file:
    lines = file.readlines()

print(f"Reading {csv_path}...")
print(f"Found {len(lines)} lines in the file")

# Parse the data
data_rows = []
for i, line in enumerate(lines[1:], 1):  # Skip header, start counting from 1
    # Remove quotes and newlines, then split by semicolon
    line = line.strip().strip('"')
    if line:  # Skip empty lines
        parts = line.split(';')
        if len(parts) >= 3:  # Ensure we have at least 3 parts
            data_rows.append(parts)
            print(f"Row {i}: Found {len(parts)} parts")
        else:
            print(f"Row {i}: Skipped (only {len(parts)} parts): {line[:50]}...")

# Create DataFrame with proper column names
columns = ['designacao_da_empresa', 'designacao_do_contrato', 'ref_do_contrato', 'morada_endereco_eletronico']
# Adjust columns based on actual data
max_parts = max(len(row) for row in data_rows) if data_rows else 4
actual_columns = columns[:max_parts]

df = pd.DataFrame(data_rows, columns=actual_columns)

print(f"\nSuccessfully loaded {len(df)} rows")
print("Columns:", df.columns.tolist())
print("\nFirst few rows:")
print(df.head())

# Step 2: Combine all columns into a single column
df['combined'] = df.apply(lambda row: ','.join(row.values.astype(str)), axis=1)

# Step 3: Save the combined data to a new CSV file with proper encoding
output_path = 'Contratos2_combined.csv'

# Try different encoding options to ensure Portuguese characters are preserved
try:
    # First try UTF-8 with BOM (helps Windows applications recognize UTF-8)
    df.to_csv(output_path, index=False, sep=',', encoding='utf-8-sig')
    print(f"\nCombined data saved to {output_path} (UTF-8 with BOM)")
except Exception as e:
    print(f"UTF-8 with BOM failed: {e}")
    try:
        # Fallback to regular UTF-8
        df.to_csv(output_path, index=False, sep=',', encoding='utf-8')
        print(f"\nCombined data saved to {output_path} (UTF-8)")
    except Exception as e:
        print(f"UTF-8 failed: {e}")
        # Last resort: use latin-1 encoding
        df.to_csv(output_path, index=False, sep=',', encoding='latin-1')
        print(f"\nCombined data saved to {output_path} (Latin-1)")

print(f"Output file contains {len(df)} rows")

# Let's also create a version with explicit Portuguese character handling
output_path_pt = 'Contratos2_combined_PT.csv'
try:
    # Save with explicit UTF-8 BOM for better Portuguese character support
    with open(output_path_pt, 'w', encoding='utf-8-sig', newline='') as f:
        df.to_csv(f, index=False, sep=',')
    print(f"Portuguese-optimized version saved to {output_path_pt}")
except Exception as e:
    print(f"Failed to create Portuguese-optimized version: {e}")