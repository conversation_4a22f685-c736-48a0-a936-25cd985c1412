#!/usr/bin/env python3
"""
Test script for document generator
"""

import pandas as pd
from docx import Document
from pathlib import Path
import logging
from typing import Dict
import sys
import re

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class DocxGenerator:
    def __init__(self, template_path: str, output_dir: str = "generated_documents"):
        """
        Initialize the DOCX generator
        
        Args:
            template_path (str): Path to the template DOCX file
            output_dir (str): Directory to save generated documents
        """
        self.template_path = Path(template_path)
        self.output_dir = Path(output_dir)
        
        # Create output directory if it doesn't exist
        self.output_dir.mkdir(exist_ok=True)
        
        # Validate template exists
        if not self.template_path.exists():
            raise FileNotFoundError(f"Template file not found: {template_path}")
        
        logger.info(f"Initialized generator with template: {template_path}")
        logger.info(f"Output directory: {output_dir}")

    def load_data_from_csv(self, csv_path: str) -> pd.DataFrame:
        """
        Load contract data from CSV file with semicolon delimiter and UTF-8 encoding
        
        Expected columns:
        - designacao_da_empresa
        - designacao_do_contrato
        - ref_do_contrato
        - morada_endereco_eletronico
        """
        try:
            df = pd.read_csv(csv_path, sep=';', encoding='utf-8')
            required_columns = [
                'designacao_da_empresa',
                'designacao_do_contrato',
                'ref_do_contrato',
                'morada_endereco_eletronico'
            ]
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {missing_columns}")
            
            # Replace NaN with empty strings and ensure all data is string type
            df = df.fillna('')
            for col in required_columns:
                df[col] = df[col].astype(str)
            
            logger.info(f"Loaded {len(df)} records from {csv_path}")
            logger.info(f"Columns: {list(df.columns)}")
            return df
        except Exception as e:
            logger.error(f"Error loading CSV: {e}")
            raise

    def replace_text_in_paragraph(self, paragraph, replacements: Dict[str, str]):
        """
        Replace placeholder text in a paragraph while preserving formatting
        """
        full_text = ""
        for run in paragraph.runs:
            full_text += run.text
        
        # Check if any placeholder exists in this paragraph
        has_replacement = False
        for placeholder in replacements.keys():
            if placeholder in full_text:
                has_replacement = True
                break
        
        if has_replacement:
            # Replace text while preserving the first run's formatting
            for placeholder, replacement in replacements.items():
                full_text = full_text.replace(placeholder, replacement)
            
            # Clear all runs and set the new text in the first run
            for i, run in enumerate(paragraph.runs):
                if i == 0:
                    run.text = full_text
                else:
                    run.text = ""

    def replace_text_in_tables(self, doc, replacements: Dict[str, str]):
        """
        Replace placeholder text in tables
        """
        for table in doc.tables:
            for row in table.rows:
                for cell in row.cells:
                    for paragraph in cell.paragraphs:
                        self.replace_text_in_paragraph(paragraph, replacements)

    def sanitize_filename(self, filename: str) -> str:
        """
        Sanitize filename by removing invalid characters and ensuring uniqueness
        """
        # Replace invalid characters with underscores
        filename = re.sub(r'[^\w\-_\.]', '_', filename)
        # Ensure filename doesn't start with a dot or space
        filename = filename.strip().lstrip('.')
        # Truncate to a reasonable length (e.g., 200 characters)
        filename = filename[:200]
        # Add .docx extension if not present
        if not filename.endswith('.docx'):
            filename += '.docx'
        return filename

    def generate_document(self, template_doc: Document, data_row: pd.Series, output_filename: str):
        """
        Generate a single document from template and data
        """
        # Create replacements dictionary
        replacements = {}
        for column in data_row.index:
            placeholder = f"{{{{{column}}}}}"  # Format: {{column_name}}
            replacements[placeholder] = str(data_row[column])
        
        logger.info(f"Replacements: {replacements}")
        
        # Replace text in paragraphs
        for paragraph in template_doc.paragraphs:
            self.replace_text_in_paragraph(paragraph, replacements)
        
        # Replace text in tables
        self.replace_text_in_tables(template_doc, replacements)
        
        # Replace text in headers and footers
        for section in template_doc.sections:
            # Header
            header = section.header
            for paragraph in header.paragraphs:
                self.replace_text_in_paragraph(paragraph, replacements)
            
            # Footer
            footer = section.footer
            for paragraph in footer.paragraphs:
                self.replace_text_in_paragraph(paragraph, replacements)
        
        # Save the document
        output_path = self.output_dir / output_filename
        template_doc.save(str(output_path))
        logger.info(f"Document saved: {output_path}")
        return output_path

    def generate_all_documents(self, data: pd.DataFrame, filename_column: str = 'ref_do_contrato'):
        """
        Generate all documents from the dataset
        """
        successful = 0
        failed = 0
        
        logger.info(f"Starting generation of {len(data)} documents...")
        
        for index, row in data.iterrows():
            try:
                # Load fresh template for each document
                template_doc = Document(str(self.template_path))
                
                # Generate filename
                filename_base = str(row[filename_column]).replace('/', '_').replace('\\', '_')
                filename = self.sanitize_filename(filename_base)
                
                # Generate document
                output_path = self.generate_document(template_doc, row, filename)
                
                successful += 1
                logger.info(f"Generated document {index + 1}/{len(data)}: {filename}")
                
            except Exception as e:
                failed += 1
                logger.error(f"Failed to generate document for row {index}: {e}")
        
        logger.info(f"Generation complete! Successful: {successful}, Failed: {failed}")
        return successful, failed

def main():
    """
    Test the document generator
    """
    try:
        print("Testing document generator...")
        
        # Initialize generator
        generator = DocxGenerator("contrato_template.docx", "contratos_gerados")
        
        # Load data from CSV
        data = generator.load_data_from_csv('Contratos2_for_generator.csv')
        print(f"Loaded {len(data)} rows")
        print("Columns:", list(data.columns))
        print("\nFirst row data:")
        for col in data.columns:
            print(f"  {col}: {repr(data.iloc[0][col])}")
        
        # Generate documents
        print(f"\nGenerating {len(data)} documents...")
        successful, failed = generator.generate_all_documents(data)
        
        print(f"\nGeneration completed:")
        print(f"✓ Successful: {successful}")
        print(f"✗ Failed: {failed}")
        print(f"📁 Output directory: contratos_gerados/")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
